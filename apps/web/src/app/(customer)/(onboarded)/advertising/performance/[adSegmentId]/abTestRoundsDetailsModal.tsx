import { api } from "@/trpc/client";
import { <PERSON>, Eye, Trophy } from "lucide-react";

import { Badge } from "@kalos/ui/badge";
import { <PERSON><PERSON> } from "@kalos/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@kalos/ui/dialog";

import { AbTestType } from "../../../../../../../../../backend/src/modules/advertising/abTest/internal/domain/abTestType.valueObject";

interface AbTestRoundsDetailsModalProps {
  abTestId: string;
  abTestType: AbTestType;
  adSegmentId: string;
}

export function AbTestRoundsDetailsModal({
  abTestId,
  abTestType,
  adSegmentId,
}: AbTestRoundsDetailsModalProps) {
  const abTestQuery = api.v2.ads.newAbTestController.getAbTestData.useQuery({
    abTestId: abTestId,
    type: abTestType,
    adSegmentId: adSegmentId,
  });

  // Get rounds data from abTestData
  const pastRoundsData = abTestQuery.data?.pastRounds || [];
  const currentRoundData = abTestQuery.data?.currentRound;
  const upcomingRoundsData = abTestQuery.data?.upcomingRounds || [];

  // Helper function to get ad title
  const getAdTitle = (variant: any, role: "Current Best" | "Contender") => {
    if (!variant) return `${role}`;

    const variantDetails = variant.varientsUsedInSponsoredCreative;

    if (variantDetails.adFormatType === "SPONSORED_CONTENT") {
      return `${variantDetails.creativeType}`;
    } else if (variantDetails.adFormatType === "SPONSORED_INMAIL") {
      return `${variantDetails.conversationSubjectCopyType}`;
    }

    return `Ad ${variant.sponsoredCreativeId.slice(-8)}`;
  };

  // Helper function to get round data for display
  const getRoundDisplayData = async (
    roundId: string,
    status: "past" | "current" | "upcoming",
  ) => {
    if (status === "upcoming") {
      return null; // No data for upcoming rounds
    }

    // For current and past rounds, we would need to fetch the round data
    // This is a simplified version - in practice you'd use the same query pattern as in AbTestRoundRow
    return null;
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Eye className="mr-2 h-4 w-4" />
          View Details
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-[80vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Test Rounds Details
          </DialogTitle>
          <DialogDescription>
            Detailed breakdown of A/B test rounds and their performance
          </DialogDescription>
        </DialogHeader>

        <div className="mt-6 space-y-6">
          {/* Past Rounds */}
          {pastRoundsData.map((round, index) => (
            <div key={`past-${round.id}`} className="space-y-4">
              <div className="flex items-center space-x-2 border-b pb-2">
                <Trophy className="h-5 w-5 text-yellow-500" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Round #{round.roundIndex}
                </h3>
              </div>

              <div className="rounded-lg bg-gray-50 p-4">
                <div className="mb-4 grid grid-cols-3 gap-4 border-b pb-2 text-sm font-medium text-gray-600">
                  <div>Winner</div>
                  <div>Ad Name</div>
                  <div>AD ID</div>
                </div>

                <div className="space-y-3">
                  <div className="grid grid-cols-3 items-center gap-4 py-2">
                    <div className="flex items-center">
                      <Badge className="border-green-200 bg-green-100 text-green-800">
                        <Crown className="mr-1 h-3 w-3" />
                        Winner
                      </Badge>
                    </div>
                    <div className="font-medium text-gray-900">
                      {round.winner === "CURRENT_BEST"
                        ? "Previous Best"
                        : "Challenger"}
                    </div>
                    <div className="font-mono text-blue-600">-</div>
                  </div>

                  <div className="grid grid-cols-3 items-center gap-4 border-t border-gray-200 py-2">
                    <div></div>
                    <div className="text-gray-700">
                      {round.winner === "CURRENT_BEST"
                        ? "Challenger (Lost)"
                        : "Previous Best (Lost)"}
                    </div>
                    <div className="font-mono text-blue-600">-</div>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {/* Current Round */}
          {currentRoundData && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2 border-b pb-2">
                <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-500">
                  <div className="h-2 w-2 animate-pulse rounded-full bg-white"></div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">
                  Round #{currentRoundData.roundIndex}
                </h3>
                <Badge
                  variant="outline"
                  className="border-blue-200 text-blue-600"
                >
                  Current
                </Badge>
              </div>

              <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                <div className="mb-4 grid grid-cols-3 gap-4 border-b pb-2 text-sm font-medium text-gray-600">
                  <div>Winner</div>
                  <div>Ad Name</div>
                  <div>AD ID</div>
                </div>

                <div className="space-y-3">
                  <div className="grid grid-cols-3 items-center gap-4 py-2">
                    <div className="text-sm text-gray-500">In Progress</div>
                    <div className="font-medium text-gray-900">
                      Current Best
                    </div>
                    <div className="font-mono text-blue-600">-</div>
                  </div>

                  <div className="grid grid-cols-3 items-center gap-4 border-t border-gray-200 py-2">
                    <div className="text-sm text-gray-500">In Progress</div>
                    <div className="text-gray-700">Contender</div>
                    <div className="font-mono text-blue-600">-</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Upcoming Rounds */}
          {upcomingRoundsData.map((round, index) => (
            <div key={`upcoming-${round.id}`} className="space-y-4">
              <div className="flex items-center space-x-2 border-b pb-2">
                <div className="flex h-5 w-5 items-center justify-center rounded-full bg-gray-300">
                  <div className="h-2 w-2 rounded-full bg-white"></div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">
                  Round #{round.roundIndex}
                </h3>
                <Badge variant="secondary">Upcoming</Badge>
              </div>

              <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
                <div className="mb-4 grid grid-cols-3 gap-4 border-b pb-2 text-sm font-medium text-gray-600">
                  <div>Winner</div>
                  <div>Ad Name</div>
                  <div>AD ID</div>
                </div>

                <div className="space-y-3">
                  <div className="grid grid-cols-3 items-center gap-4 py-2">
                    <div className="text-sm text-gray-400">TBD</div>
                    <div className="text-gray-700">
                      Winner of Round #{round.roundIndex}
                    </div>
                    <div className="font-mono text-blue-600">-</div>
                  </div>

                  <div className="grid grid-cols-3 items-center gap-4 border-t border-gray-200 py-2">
                    <div className="text-sm text-gray-400">TBD</div>
                    <div className="text-gray-700">New Challenger</div>
                    <div className="font-mono text-blue-600">-</div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}
